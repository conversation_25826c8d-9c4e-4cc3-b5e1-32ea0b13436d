import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { useHistory, useParams, useLocation } from 'react-router-dom';
import { useQuery, useMutation } from 'react-apollo';
import { get, isEmpty } from 'lodash';

import EContentItemContentForm, {
  cookEContentClobs,
  IEContentContentEntity,
} from '../form/tabs/EContentItemContentsTab/form/EContentItemContentForm';
import ContentItemTitleWithArrows from '../form/tabs/EContentItemContentsTab/form/ContentItemTitleWithArrows';
import eContentItemContent from '../../../../../common/data/eContent/eContentItemContent.graphql';
import updateEContentItemContent from '../../../../../common/data/eContent/updateEContentItemContent.graphql';
import createEContentItemContent from '../../../../../common/data/eContent/createEContentItemContent.graphql';
import eContentItem from '../../../../../common/data/eContent/eContentItem.graphql';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import useT from '../../../../../common/components/utils/Translations/useT';
import { Active } from '../../../../../model/StatusWithDraft';
import IEContentContent from '../../../../../common/abstract/EContent/IEContentContent';
import IEContentResource from '../../../../../common/abstract/EContent/IEContentResource';
import IEContentItem from '../../../../../common/abstract/EContent/IEContentItem';
import ContentPanel from '../../../../../common/components/containers/BasicModuleLayout/ContentPanel';

interface RouteParams {
  libraryId: string;
  itemId: string;
  contentId: string;
}

const EContentItemContentFormRoute: React.FC = () => {
  const t = useT();
  const history = useHistory();
  const location = useLocation();
  const { libraryId, itemId, contentId } = useParams<RouteParams>();

  const isNew = contentId === 'new';

  // State for URL parameters needed by ContentItemTitleWithArrows
  const [searchQuery, setSearchQuery] = useState<string>();
  const [contentIndex, setContentIndex] = useState<number | null>(null);
  const [contentFilters, setContentFilters] = useState(null);
  const [updatedCount, setUpdatedCount] = useState<number>(0);

  // Parse URL parameters for ContentItemTitleWithArrows
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    if (params.has('searchQuery')) {
      setSearchQuery(params.get('searchQuery') as string);
    }
    if (params.has('contentIndex')) {
      setContentIndex(Number(params.get('contentIndex')));
    }
    if (params.has('contentFilters')) {
      const cFilters =
        params.get('contentFilters')?.split('/')[0] ||
        params.get('contentFilters');
      if (!isEmpty(cFilters)) setContentFilters(JSON.parse(cFilters as string));
    }
  }, [location.search]);

  const mainItemId = parseInt(itemId, 10);

  // Query for existing content
  const {
    data: contentData,
    loading: contentLoading,
    error: contentError,
  } = useQuery(eContentItemContent, {
    variables: { id: parseInt(contentId, 10) },
    skip: isNew,
  });

  // Query for item information to get resource
  const { data: itemData, loading: itemLoading } = useQuery(eContentItem, {
    variables: { id: parseInt(itemId, 10) },
  });

  // Mutations
  const [updateContentMutation] = useMutation(updateEContentItemContent);
  const [createContentMutation] = useMutation(createEContentItemContent);

  const content = useMemo(() => {
    if (isNew) {
      return {
        itemId: parseInt(itemId, 10),
        status: Active.value,
        clobs: {},
      } as IEContentContent;
    }
    return get(contentData, 'eContentItemContent', {}) as IEContentContent;
  }, [contentData, isNew, itemId]);

  const item = useMemo(
    () => get(itemData, 'eContentItem', {}) as IEContentItem,
    [itemData],
  );

  const resource = useMemo(() => item?.resource || ({} as IEContentResource), [
    item,
  ]);

  const cookedClobs = useMemo(
    () => cookEContentClobs(content.clobs, resource.attributes),
    [content, resource.attributes],
  );

  const handleGoBack = useCallback(() => {
    const fromPage = get(location, 'state.fromPage');
    if (fromPage === 'contentview') {
      history.push(`/e-content/libraries/edit/${libraryId}/content-view`);
    } else {
      history.push(
        `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents-list`,
      );
    }
  }, [history, libraryId, itemId, location]);

  const handleSubmit = useCallback(
    async (values: IEContentContentEntity) => {
      try {
        if (isNew) {
          const result = await createContentMutation({
            variables: {
              params: {
                ...values,
                itemId: parseInt(itemId, 10),
              },
            },
          });
          const newContentId = get(result, 'data.createEContentItemContent.id');
          if (newContentId) {
            history.replace(
              `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents-list/edit/${newContentId}`,
            );
          }
        } else {
          await updateContentMutation({
            variables: {
              id: parseInt(contentId, 10),
              params: {
                ...values,
              },
            },
          });
        }
        setUpdatedCount(prev => prev + 1);
      } catch (error) {
        console.error('Error saving content:', error);
      }
    },
    [
      isNew,
      createContentMutation,
      updateContentMutation,
      itemId,
      contentId,
      history,
      libraryId,
    ],
  );

  const goToContents = useCallback(() => {
    history.push(
      `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents-list`,
    );
  }, [history, libraryId, itemId]);

  const goToCreate = useCallback(() => {
    history.push(
      `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents-list/edit/new`,
    );
  }, [history, libraryId, itemId]);

  const goToCreateItem = useCallback(() => {
    history.push(`/e-content/libraries/edit/${libraryId}/create`);
  }, [history, libraryId]);

  const titleContent = cookedClobs?.heading?.content;

  const renderTitle = useCallback(
    () => (
      <div className="">
        <ContentItemTitleWithArrows
          contentFilters={contentFilters}
          contentIndex={contentIndex}
          itemId={parseInt(contentId, 10)}
          mainItemId={mainItemId}
          searchQuery={searchQuery}
          title={titleContent}
          updatedCount={updatedCount}
        />
      </div>
    ),
    [
      contentFilters,
      contentIndex,
      contentId,
      mainItemId,
      searchQuery,
      titleContent,
      updatedCount,
    ],
  );

  if (contentLoading || itemLoading) {
    return <SpinnerError loading />;
  }

  if (contentError) {
    return <SpinnerError error={contentError} />;
  }

  if (!content && !isNew) {
    return <div>{t('Content not found')}</div>;
  }

  return (
    <ContentPanel
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      renderTitle={renderTitle}
      title={cookedClobs?.heading?.content}
    >
      <EContentItemContentForm
        entity={content}
        floatButton={false}
        goToContents={goToContents}
        goToCreate={goToCreate}
        goToCreateItem={goToCreateItem}
        resource={resource}
        onGoBack={handleGoBack}
        onSubmit={handleSubmit}
      />
    </ContentPanel>
  );
};

export default EContentItemContentFormRoute;
